import json
import asyncio
import queue
import threading
from flask import Blueprint, request, Response, jsonify, stream_with_context
from .services.query_service import query_streaming_service
from typing import Any, List, AsyncGenerator, Tuple
import pymysql
from sqlalchemy import text
from .services.database import engine

# 创建一个蓝图
api_bp = Blueprint('api_bp', __name__)

# --- 输入联想建议部分 (保持不变) ---
SUGGESTION_LIST: List[str] = [
    "按学历统计专家数量分布",
    "按职称统计专家数量，并生成图表",
    "各个级别的专家有多少人？",
    "查询姓张的专家的姓名和邮箱",
    "列出所有研究员的姓名和研究方向",
    "开发状态为'开发成功'的专家有哪些？",
    "统计来自'艾思专家'和'智库专家'的专家数量",
    "最近一个月内新创建的专家是谁？",
    "查询所有是'院士'或'IEEE Fellow'的专家头衔和姓名"
]


def get_suggestions(query: str) -> List[str]:
    """
    根据用户输入的前缀，从建议列表中筛选匹配项。
    """
    if not query:
        return []
    # 使用 'in' 进行模糊匹配，忽略大小写
    return [
        suggestion for suggestion in SUGGESTION_LIST
        if query.lower() in suggestion.lower()
    ]


@api_bp.route('/suggestions', methods=['POST'])
def handle_suggestions():
    """
    处理输入联想建议的请求
    """
    data = request.get_json()
    if not data or 'query' not in data:
        return jsonify({"error": "请求体中必须包含 'query' 字段"}), 400

    query = data['query']
    suggestions = get_suggestions(query)
    return jsonify({"suggestions": suggestions[:5]})


# --- 流式查询部分 (核心修改) ---
@api_bp.route('/query', methods=['POST'])
def handle_query():
    """
    处理来自前端的自然语言查询请求。
    此版本使用 线程+队列 的模式，以兼容WSGI服务器的同步流处理。
    """
    data = request.get_json()
    if not data or 'question' not in data:
        return jsonify({"error": "请求体中必须包含 'question' 字段"}), 400

    question = data['question']

    def generate_events_sync():
        """
        这是一个同步生成器，Flask/Werkzeug可以安全地迭代它。
        它从队列中获取由后台异步任务产生的数据。
        """
        q = queue.Queue()

        def run_async_service():
            """
            此函数在后台线程中运行，负责执行异步的流式服务。
            """
            # 为新线程创建并设置新的asyncio事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 定义一个协程来消费异步生成器并将结果放入队列
                async def consume_stream_and_queue_results():
                    try:
                        async for item in query_streaming_service(question):
                            q.put(item)
                    except Exception as e:
                        # 如果异步任务内部出错，将错误信息放入队列
                        print(f"Error in async task: {e}")
                        error_payload = json.dumps({"error": "异步服务执行出错", "details": str(e)})
                        q.put(f"event: error\ndata: {error_payload}\n\n")

                # 运行协程直到完成
                loop.run_until_complete(consume_stream_and_queue_results())
            finally:
                # 确保在任务结束时（无论成功或失败）都放入一个终止信号
                q.put(None)
                loop.close()

        # 启动后台线程
        thread = threading.Thread(target=run_async_service)
        thread.start()

        # 在主线程中，从队列中获取数    据并yield出去
        while True:
            item = q.get()  # 这是一个阻塞操作，会等待队列中有数据
            if item is None:
                # 收到终止信号，退出循环
                break
            yield item

    # 使用 stream_with_context，因为我们的生成器现在是同步的
    return Response(stream_with_context(generate_events_sync()), mimetype='text/event-stream')

def clean_sql(sql: str) -> str:
    # ... 现有代码 ...
    pass

def run_sql(sql: str) -> List[Tuple[Any, ...]]:
    """
    [重构] 使用 SQLAlchemy 连接池执行 SQL。
    这会复用数据库连接，显著提升性能。
    
    Returns:
        List[Tuple[Any, ...]]: 查询结果列表
    """
    try:
        with engine.connect() as conn:
            result = conn.execute(text(sql))
            # 将 SQLAlchemy 的 Row 对象转换为标准的 list[tuple]
            return [tuple(row) for row in result]
    except Exception as e:
        print(f"SQL执行错误: {e}")
        return [("SQL_EXECUTION_ERROR", str(e))]


def run_sql_with_columns(sql: str) -> dict[str, Any]:
    """
    执行SQL查询并返回结果，包含列名信息。
    
    Args:
        sql (str): 要执行的SQL查询语句
        
    Returns:
        Dict[str, Any]: 包含数据和列名的字典
    """
    try:
        with engine.connect() as conn:
            # 执行查询
            result = conn.execute(text(sql))
            
            # 获取列名
            column_names = result.keys()
            
            # 获取数据
            data = [tuple(row) for row in result]
            
            return {
                "data": data,
                "columns": column_names
            }
    except Exception as e:
        print(f"SQL执行错误: {e}")
        return {
            "data": [("SQL_EXECUTION_ERROR", str(e))],
            "columns": ["error", "message"]
        }
