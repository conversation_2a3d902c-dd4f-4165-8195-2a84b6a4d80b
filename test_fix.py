#!/usr/bin/env python3
"""
测试修复后的final_summary_chain
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.chains import final_summary_chain


async def test_final_summary_chain():
    """测试final_summary_chain是否能正常工作"""
    print("测试final_summary_chain...")
    
    try:
        # 测试数据
        test_input = {
            "question": "有多少专家？",
            "sql_query": "SELECT COUNT(*) FROM erp_expert;",
            "raw_data": "[(100,)]",
            "analysis_results": ""
        }
        
        # 测试同步调用
        result = await final_summary_chain.ainvoke(test_input)
        print("✅ final_summary_chain 调用成功")
        print(f"结果长度: {len(result)} 字符")
        
        # 测试流式调用
        print("\n测试流式调用...")
        chunk_count = 0
        async for chunk in final_summary_chain.astream(test_input):
            chunk_count += 1
            if chunk_count <= 3:  # 只显示前3个chunk
                print(f"Chunk {chunk_count}: {chunk[:50]}...")
        
        print(f"✅ 流式调用成功，共收到 {chunk_count} 个chunk")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True


async def main():
    """主测试函数"""
    print("开始测试修复...")
    
    success = await test_final_summary_chain()
    
    if success:
        print("\n🎉 修复验证成功！")
        print("final_summary_chain 现在可以正常工作了")
    else:
        print("\n❌ 修复验证失败")
        print("需要进一步检查问题")


if __name__ == "__main__":
    asyncio.run(main())
